# API 设计指导文档

本文档规范了项目中 API 响应模型、异常处理和文档格式的设计标准，确保开发团队遵循统一的最佳实践。

## 📋 目录

- [响应模型设计](#响应模型设计)
- [异常处理模式](#异常处理模式)
- [API 文档规范](#api-文档规范)
- [开发实践](#开发实践)
- [示例代码](#示例代码)

## 🎯 响应模型设计

### 统一响应格式

所有 API 响应都必须遵循统一的**双码设计**格式：

#### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 错误响应

```json
{
  "success": false,
  "message": "错误描述",
  "http_code": 400,
  "business_code": "SPECIFIC_ERROR_CODE",
  "level": "error",
  "details": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 字段说明

- **`http_code`** (数字): HTTP状态码，前端用于判断请求成功/失败
  - `200`: 成功
  - `400`: 业务失败
  - `422`: 数据验证失败
  - `404`: 资源不存在
  - `401`: 认证失败
  - `403`: 权限不足
  - `500`: 服务器错误

- **`business_code`** (字符串): 业务状态码，前端用于精确处理业务逻辑
  - `"SUCCESS"`: 操作成功
  - `"TEACHER_EMAIL_EXISTS"`: 教师邮箱已存在
  - `"AUTHENTICATION_FAILED"`: 认证失败
  - `"PERMISSION_DENIED"`: 权限不足
  - 等等...

### 响应模型类型

#### 1. DataResponse[T] - 单个数据响应

```python
@router.get("/{id}", response_model=DataResponse[UserRead])
def get_user(id: int):
    user = user_service.get_user(id)
    return success_response(user, "获取用户成功")
```

#### 2. ListResponse[T] - 列表响应

```python
@router.get("/", response_model=ListResponse[UserRead])
def get_users():
    users = user_service.get_users()
    return list_response(users, len(users), "获取用户列表成功")
```

#### 3. PageResponse[T] - 分页响应

```python
@router.get("/", response_model=PageResponse[UserRead])
def get_users_paginated(page: int = 1, size: int = 20):
    users, total = user_service.get_users_paginated(page, size)
    return page_response(users, total, page, size, "获取用户列表成功")
```

#### 4. MessageResponse - 消息响应（无数据）

```python
@router.post("/{id}/activate", response_model=MessageResponse)
def activate_user(id: int):
    user_service.activate_user(id)
    return message_response("用户激活成功")
```

## ⚠️ 异常处理模式

### 三层异常架构

#### 1. 全局层 - GlobalErrorCode

包含成功状态码和需要全局处理的通用错误：

```python
class GlobalErrorCode(BaseErrorCode):
    # 成功状态码
    SUCCESS = "SUCCESS"

    # 格式：[CATEGORY_]SPECIFIC_ERROR
    # 通用业务错误
    BUSINESS_ERROR = "BUSINESS_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"

    # 认证授权相关 - 需要全局处理
    AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    TOKEN_INVALID = "TOKEN_INVALID"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"

    # 系统级错误 - 需要全局处理
    DATABASE_ERROR = "DATABASE_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"
```

#### 2. 模块层 - 业务模块特定错误码

```python
class UserErrorCode(BaseErrorCode):
    """用户模块错误码"""
    # 格式：MODULE_SPECIFIC_ERROR
    EMAIL_EXISTS = "USER_EMAIL_EXISTS"
    PHONE_EXISTS = "USER_PHONE_EXISTS"
    ACCOUNT_DISABLED = "USER_ACCOUNT_DISABLED"
```

#### 3. 服务层 - 业务异常类

```python
class UserBusinessException(BusinessException):
    """用户业务异常"""

    @classmethod
    def email_already_exists(cls, email: str):
        return cls(
            f"邮箱 {email} 已存在",
            UserErrorCode.EMAIL_EXISTS,
            ErrorLevel.WARNING
        )

    @classmethod
    def general_error(cls, message: str):
        return cls(message)  # 使用默认错误码
```

### 异常使用原则

#### ✅ 正确使用

```python
# 1. 大部分业务异常使用默认错误码
if not user:
    raise BusinessException("用户不存在")

# 2. 只有客户端需要特殊处理时才指定具体错误码
if user.status == UserStatus.DISABLED:
    raise UserBusinessException.account_disabled()

# 3. 使用工厂方法创建异常
raise UserBusinessException.email_already_exists(email)
```

#### ❌ 错误使用

```python
# 不要为每个业务场景都定义错误码
raise BusinessException("用户不存在", UserErrorCode.USER_NOT_FOUND)  # 过度设计

# 不要在全局错误码中定义业务特定错误
class GlobalErrorCode(BaseErrorCode):
    USER_EMAIL_EXISTS = "USER_EMAIL_EXISTS"  # 应该在模块层定义
```

### 错误级别指导

- **INFO**: 信息提示，不影响业务流程
- **WARNING**: 警告，业务可继续但需要用户注意
- **ERROR**: 错误，业务无法继续，需要用户处理
- **CRITICAL**: 严重错误，系统级问题，需要技术介入

## 📖 API 文档规范

### 全局响应配置

在 `app/api/common/docs.py` 中配置全局响应：

```python
GLOBAL_RESPONSES = {
    400: {
        "description": "业务逻辑错误",
        "content": { ... }
    },
    422: {
        "description": "数据验证失败",
        "content": { ... }
    }
}
```

### 路由响应配置

根据接口特性选择合适的响应配置：

#### 1. 普通接口（无特殊响应）

```python
@router.get("/", response_model=ListResponse[UserRead])
def get_users():
    # 自动包含全局响应：400, 422
    pass
```

#### 2. 需要认证的接口

```python
@router.get("/profile", response_model=DataResponse[UserRead], responses=AUTH_ONLY_RESPONSES)
def get_profile():
    # 包含：400, 401, 422
    pass
```

#### 3. 需要权限的接口

```python
@router.post("/admin/users", response_model=DataResponse[UserRead], responses=PERMISSION_ONLY_RESPONSES)
def create_user_admin():
    # 包含：400, 403, 422
    pass
```

#### 4. 需要认证+权限的接口

```python
@router.delete("/{id}", response_model=MessageResponse, responses=RESOURCE_RESPONSES)
def delete_user(id: int):
    # 包含：400, 401, 403, 422
    pass
```

#### 5. 模块特定错误的接口

```python
USER_ERROR_RESPONSES = create_error_responses([
    UserErrorCode.EMAIL_EXISTS,
    UserErrorCode.PHONE_EXISTS
])

@router.post("/", response_model=DataResponse[UserRead], responses=USER_ERROR_RESPONSES)
def create_user():
    # 包含：400（含特定业务错误示例）, 422
    pass
```

### 文档标签和描述

为每个路由模块添加清晰的标签和描述：

```python
# 在 app/api/v1/api.py 中
api_router.include_router(users_router, prefix="/users", tags=["用户管理"])

# 在 app/api/common/docs.py 中
TAGS_METADATA = [
    {
        "name": "用户管理",
        "description": "系统用户管理接口，包括用户的增删改查、权限管理等操作",
    }
]
```

## 🛠️ 开发实践

### Service 层异常处理

```python
class UserService:
    def create_user(self, user_data: UserCreate) -> User:
        # 检查邮箱是否存在
        if self.get_user_by_email(user_data.email):
            raise UserBusinessException.email_already_exists(user_data.email)

        # 一般业务错误使用默认异常
        if not self.validate_user_data(user_data):
            raise BusinessException("用户数据验证失败")

        # ... 业务逻辑
        return user
```

### Router 层异常处理

```python
@router.post("/", response_model=DataResponse[UserRead], responses=USER_ERROR_RESPONSES)
def create_user(
    user_data: UserCreate,
    user_service: UserService = Depends()
):
    """创建用户"""
    # Service层抛出异常，由全局异常处理器统一处理
    user = user_service.create_user(user_data)
    return success_response(user, "用户创建成功")
```

### 响应构建

```python
# ✅ 推荐：使用便捷函数
return success_response(user, "操作成功")
return list_response(users, total, "获取成功")
return message_response("删除成功")

# ❌ 不推荐：手动构建响应
return DataResponse(success=True, data=user, message="操作成功")
```

## 📝 示例代码

### 完整的模块示例

```python
# exceptions.py
class ProductErrorCode(BaseErrorCode):
    OUT_OF_STOCK = "PRODUCT_OUT_OF_STOCK"
    PRICE_CHANGED = "PRODUCT_PRICE_CHANGED"

class ProductBusinessException(BusinessException):
    @classmethod
    def out_of_stock(cls, product_name: str):
        return cls(
            f"商品 {product_name} 库存不足",
            ProductErrorCode.OUT_OF_STOCK,
            ErrorLevel.WARNING
        )

# service.py
class ProductService:
    def purchase_product(self, product_id: int, quantity: int):
        product = self.get_product(product_id)
        if not product:
            raise BusinessException("商品不存在")

        if product.stock < quantity:
            raise ProductBusinessException.out_of_stock(product.name)

        # ... 业务逻辑

# router.py
PRODUCT_ERROR_RESPONSES = create_error_responses([
    ProductErrorCode.OUT_OF_STOCK,
    ProductErrorCode.PRICE_CHANGED
])

@router.post("/{product_id}/purchase",
             response_model=DataResponse[PurchaseResult],
             responses=PRODUCT_ERROR_RESPONSES)
def purchase_product(
    product_id: int,
    quantity: int,
    product_service: ProductService = Depends()
):
    """购买商品"""
    result = product_service.purchase_product(product_id, quantity)
    return success_response(result, "购买成功")
```

## 💻 前端使用指南

### 响应处理逻辑

前端应该按照以下逻辑处理API响应：

```typescript
// 1. 统一的API响应接口
interface APIResponse<T = any> {
  success: boolean;
  message: string;
  http_code: number;        // HTTP状态码
  business_code: string;    // 业务状态码
  data?: T;
  details?: any;
  timestamp: string;
}

// 2. 统一的响应处理函数
async function handleAPIResponse<T>(response: Response): Promise<T> {
  const data: APIResponse<T> = await response.json();

  // 首先判断HTTP状态码
  if (data.http_code !== 200) {
    // 业务失败，显示错误信息
    showError(data.message);

    // 根据业务状态码做精确处理
    switch (data.business_code) {
      case 'AUTHENTICATION_FAILED':
      case 'TOKEN_EXPIRED':
        // 认证失败，跳转登录
        logout();
        break;

      case 'PERMISSION_DENIED':
        // 权限不足，跳转首页
        redirectToHome();
        break;

      case 'TEACHER_EMAIL_EXISTS':
        // 邮箱已存在，高亮邮箱字段
        highlightEmailField();
        break;

      case 'VALIDATION_ERROR':
        // 数据验证失败，显示详细错误
        showValidationErrors(data.details);
        break;

      default:
        // 其他业务错误，显示通用错误信息
        break;
    }

    throw new APIError(data.message, data.business_code);
  }

  // 业务成功，返回数据
  return data.data;
}

// 3. 使用示例
async function createTeacher(teacherData: TeacherCreate) {
  try {
    const teacher = await handleAPIResponse<Teacher>(
      fetch('/api/v1/teachers', {
        method: 'POST',
        body: JSON.stringify(teacherData)
      })
    );

    // 处理成功数据
    showSuccess('教师创建成功');
    return teacher;

  } catch (error) {
    // 错误已经在 handleAPIResponse 中处理
    console.error('创建教师失败:', error);
  }
}
```

### 错误码处理策略

```typescript
// 定义业务错误码常量
const BusinessCode = {
  SUCCESS: 'SUCCESS',

  // 认证相关 - 需要特殊处理
  AUTHENTICATION_FAILED: 'AUTHENTICATION_FAILED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  PERMISSION_DENIED: 'PERMISSION_DENIED',

  // 教师模块
  TEACHER_EMAIL_EXISTS: 'TEACHER_EMAIL_EXISTS',
  TEACHER_PHONE_EXISTS: 'TEACHER_PHONE_EXISTS',

  // 标签模块
  TAG_CATEGORY_NAME_EXISTS: 'TAG_CATEGORY_NAME_EXISTS',
  TAG_NAME_EXISTS: 'TAG_NAME_EXISTS',
} as const;

// 错误处理映射
const errorHandlers = {
  [BusinessCode.AUTHENTICATION_FAILED]: () => logout(),
  [BusinessCode.TOKEN_EXPIRED]: () => logout(),
  [BusinessCode.PERMISSION_DENIED]: () => redirectToHome(),
  [BusinessCode.TEACHER_EMAIL_EXISTS]: () => highlightField('email'),
  [BusinessCode.TEACHER_PHONE_EXISTS]: () => highlightField('phone'),
  // ... 更多处理器
};
```

## 💻 前端使用指南 (Vue-Pure-Admin)

### 技术栈对接

本项目前端采用 **Vue 3 + TypeScript + Element Plus + TailwindCSS** 技术栈，基于 Vue-Pure-Admin 企业级管理系统模板。

### API 响应处理

#### 1. 统一的 API 响应接口定义

```typescript
// types/api.ts
export interface APIResponse<T = any> {
  success: boolean;
  message: string;
  http_code: number;        // HTTP状态码
  business_code: string;    // 业务状态码
  data?: T;
  details?: any;
  timestamp: string;
}

// 分页响应
export interface PageResponse<T> extends APIResponse<T[]> {
  pagination: {
    page: number;
    size: number;
    total: number;
    pages: number;
  };
}
```

#### 2. 基于 Axios 的请求封装

```typescript
// utils/request.ts
import axios from "axios";
import { ElMessage, ElMessageBox } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";

// 业务状态码常量
export const BusinessCode = {
  SUCCESS: "SUCCESS",
  AUTHENTICATION_FAILED: "AUTHENTICATION_FAILED",
  TOKEN_EXPIRED: "TOKEN_EXPIRED",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  TEACHER_EMAIL_EXISTS: "TEACHER_EMAIL_EXISTS",
  TAG_CATEGORY_NAME_EXISTS: "TAG_CATEGORY_NAME_EXISTS",
  VALIDATION_ERROR: "VALIDATION_ERROR"
} as const;

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000
});

// 响应拦截器
service.interceptors.response.use(
  response => {
    const data: APIResponse = response.data;

    // 判断业务成功/失败
    if (data.http_code !== 200) {
      // 显示错误信息
      ElMessage.error(data.message);

      // 根据业务码做精确处理
      switch (data.business_code) {
        case BusinessCode.AUTHENTICATION_FAILED:
        case BusinessCode.TOKEN_EXPIRED:
          ElMessageBox.confirm("登录已过期，请重新登录", "提示", {
            type: "warning"
          }).then(() => {
            useUserStoreHook().logOut();
          });
          break;

        case BusinessCode.PERMISSION_DENIED:
          ElMessage.warning("权限不足，无法执行此操作");
          break;

        case BusinessCode.VALIDATION_ERROR:
          // 显示详细验证错误
          if (data.details?.validation_errors) {
            ElMessage.error(`数据验证失败: ${data.details.validation_errors}`);
          }
          break;
      }

      return Promise.reject(new Error(data.message));
    }

    return data;
  },
  error => {
    ElMessage.error("网络请求失败");
    return Promise.reject(error);
  }
);
```

#### 3. API 服务层封装

```typescript
// api/teacher.ts
import { http } from "@/utils/http";
import type { APIResponse, PageResponse } from "@/types/api";

export interface Teacher {
  id: number;
  name: string;
  email: string;
  phone?: string;
  // ... 其他字段
}

export interface TeacherCreate {
  name: string;
  email: string;
  phone?: string;
  // ... 其他字段
}

export interface TeacherQuery {
  page?: number;
  size?: number;
  name?: string;
  email?: string;
}

// 教师 API 服务
export const teacherApi = {
  // 创建教师
  create: (data: TeacherCreate): Promise<APIResponse<Teacher>> => {
    return http.post("/api/v1/teachers", data);
  },

  // 获取教师列表
  list: (params: TeacherQuery): Promise<PageResponse<Teacher>> => {
    return http.get("/api/v1/teachers", { params });
  },

  // 获取教师详情
  get: (id: number): Promise<APIResponse<Teacher>> => {
    return http.get(`/api/v1/teachers/${id}`);
  },

  // 更新教师
  update: (id: number, data: Partial<TeacherCreate>): Promise<APIResponse<Teacher>> => {
    return http.put(`/api/v1/teachers/${id}`, data);
  },

  // 删除教师
  delete: (id: number): Promise<APIResponse<null>> => {
    return http.delete(`/api/v1/teachers/${id}`);
  }
};
```

#### 4. Vue 组件中的使用示例

```vue
<!-- views/teacher/index.vue -->
<template>
  <div class="main">
    <el-card>
      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline>
        <el-form-item label="教师姓名">
          <el-input v-model="searchForm.name" placeholder="请输入教师姓名" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="mb-4">
        <el-button type="primary" @click="handleCreate">新增教师</el-button>
      </div>

      <!-- 数据表格 -->
      <pure-table
        :data="tableData"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="handleSizeChange"
        @page-current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 新增/编辑对话框 -->
    <TeacherDialog
      v-model:visible="dialogVisible"
      :teacher="currentTeacher"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { teacherApi, type Teacher, type TeacherQuery } from "@/api/teacher";
import TeacherDialog from "./components/TeacherDialog.vue";

// 响应式数据
const tableData = ref<Teacher[]>([]);
const searchForm = ref<TeacherQuery>({});
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
});
const dialogVisible = ref(false);
const currentTeacher = ref<Teacher | null>(null);

// 表格列配置
const columns = [
  { label: "ID", prop: "id" },
  { label: "姓名", prop: "name" },
  { label: "邮箱", prop: "email" },
  { label: "手机号", prop: "phone" },
  {
    label: "操作",
    slot: "operation"
  }
];

// 获取教师列表
const getTeacherList = async () => {
  try {
    const response = await teacherApi.list({
      ...searchForm.value,
      page: pagination.value.page,
      size: pagination.value.size
    });

    tableData.value = response.data || [];
    pagination.value.total = response.pagination?.total || 0;

  } catch (error) {
    // 错误已在拦截器中处理
    console.error("获取教师列表失败:", error);
  }
};

// 搜索
const handleSearch = () => {
  pagination.value.page = 1;
  getTeacherList();
};

// 重置
const handleReset = () => {
  searchForm.value = {};
  handleSearch();
};

// 新增教师
const handleCreate = () => {
  currentTeacher.value = null;
  dialogVisible.value = true;
};

// 编辑教师
const handleEdit = (teacher: Teacher) => {
  currentTeacher.value = teacher;
  dialogVisible.value = true;
};

// 删除教师
const handleDelete = async (teacher: Teacher) => {
  try {
    await ElMessageBox.confirm(`确定删除教师 ${teacher.name} 吗？`, "提示", {
      type: "warning"
    });

    await teacherApi.delete(teacher.id);
    ElMessage.success("删除成功");
    getTeacherList();

  } catch (error) {
    if (error !== "cancel") {
      console.error("删除教师失败:", error);
    }
  }
};

// 对话框成功回调
const handleDialogSuccess = () => {
  dialogVisible.value = false;
  getTeacherList();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.size = size;
  getTeacherList();
};

const handleCurrentChange = (page: number) => {
  pagination.value.page = page;
  getTeacherList();
};

// 初始化
onMounted(() => {
  getTeacherList();
});
</script>
```

#### 5. 表单验证与错误处理

```vue
<!-- components/TeacherDialog.vue -->
<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑教师' : '新增教师'"
    width="600px"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name" placeholder="请输入教师姓名" />
      </el-form-item>

      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="form.email"
          placeholder="请输入邮箱地址"
          :class="{ 'error-highlight': emailError }"
        />
        <div v-if="emailError" class="text-red-500 text-sm mt-1">
          {{ emailError }}
        </div>
      </el-form-item>

      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="form.phone"
          placeholder="请输入手机号"
          :class="{ 'error-highlight': phoneError }"
        />
        <div v-if="phoneError" class="text-red-500 text-sm mt-1">
          {{ phoneError }}
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { teacherApi, type Teacher, type TeacherCreate, BusinessCode } from "@/api/teacher";

// Props & Emits
interface Props {
  visible: boolean;
  teacher?: Teacher | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  "update:visible": [value: boolean];
  success: [];
}>();

// 响应式数据
const formRef = ref();
const loading = ref(false);
const emailError = ref("");
const phoneError = ref("");

const form = ref<TeacherCreate>({
  name: "",
  email: "",
  phone: ""
});

// 计算属性
const isEdit = computed(() => !!props.teacher);

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入教师姓名", trigger: "blur" }],
  email: [
    { required: true, message: "请输入邮箱地址", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" }
  ]
};

// 监听教师数据变化
watch(
  () => props.teacher,
  (teacher) => {
    if (teacher) {
      form.value = { ...teacher };
    } else {
      form.value = { name: "", email: "", phone: "" };
    }
    // 清除错误提示
    emailError.value = "";
    phoneError.value = "";
  },
  { immediate: true }
);

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    loading.value = true;
    emailError.value = "";
    phoneError.value = "";

    if (isEdit.value) {
      await teacherApi.update(props.teacher!.id, form.value);
      ElMessage.success("更新成功");
    } else {
      await teacherApi.create(form.value);
      ElMessage.success("创建成功");
    }

    emit("success");

  } catch (error: any) {
    // 处理特定的业务错误
    if (error.business_code === BusinessCode.TEACHER_EMAIL_EXISTS) {
      emailError.value = "邮箱已存在，请使用其他邮箱";
    } else if (error.business_code === BusinessCode.TEACHER_PHONE_EXISTS) {
      phoneError.value = "手机号已存在，请使用其他手机号";
    }

  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  emit("update:visible", false);
};
</script>

<style scoped>
.error-highlight {
  @apply border-red-500;
}
</style>
```

## 🚫 常见错误

### 1. 过度设计错误码

```python
# ❌ 错误：为每个场景都定义错误码
class UserErrorCode(BaseErrorCode):
    USER_NOT_FOUND = "USER_NOT_FOUND"
    USER_CREATE_FAILED = "USER_CREATE_FAILED"
    USER_UPDATE_FAILED = "USER_UPDATE_FAILED"
    # ... 100+ 错误码

# ✅ 正确：只为需要客户端特殊处理的场景定义错误码
class UserErrorCode(BaseErrorCode):
    EMAIL_EXISTS = "USER_EMAIL_EXISTS"      # 客户端需要高亮邮箱字段
    ACCOUNT_DISABLED = "USER_ACCOUNT_DISABLED"  # 客户端需要显示申诉入口
```

### 2. 异常处理不当

```python
# ❌ 错误：在Router层处理业务异常
@router.post("/users")
def create_user(user_data: UserCreate):
    if user_service.get_user_by_email(user_data.email):
        return {"error": "邮箱已存在"}  # 应该抛出异常

# ✅ 正确：让Service层抛出异常，全局处理器统一处理
@router.post("/users", response_model=DataResponse[UserRead])
def create_user(user_data: UserCreate):
    user = user_service.create_user(user_data)  # Service层处理异常
    return success_response(user, "用户创建成功")
```

## 🔄 版本更新

当前版本：v2.0
最后更新：2025-01-01

### 更新日志

- **v2.0**: 重大更新 - 双码响应格式设计
  - 引入 `http_code` + `business_code` 双码设计
  - 统一前后端响应处理逻辑
  - 添加 Vue-Pure-Admin 前端使用指南
  - 完善业务异常处理机制
  - 更新 API 文档示例

- **v1.0**: 初始版本，确立基本的 API 设计规范

## 📚 相关文档

- [路由设计指南](./ROUTE_DESIGN_GUIDE.md)
- [权限系统设计](../权限系统/AUTH_DEVELOPMENT_GUIDE.md)
- [测试指南](../测试相关/QUICK_TEST_GUIDE.md)

---

**注意**：本文档是活文档，会根据项目发展和最佳实践的演进持续更新。所有开发者都应该遵循此规范，如有疑问或改进建议，请及时沟通。
