"""
管理端 - 用户管理API
"""
from typing import Optional
from fastapi import APIRouter, Depends, status, Query
from sqlmodel import Session

from app.db.session import get_session
from app.core.dependencies import get_user_context, UserContext
from app.api.common.responses import DataResponse, MessageResponse, ListResponse, success_response, message_response, list_response
from app.api.common import ensure_found, ensure_success

# 用户相关导入
from app.features.users.service import UserService
from app.features.users.schemas import UserCreate, UserUpdate, UserRead, PasswordChange, UserSearchParams
from app.features.users.models import UserRole, UserStatus

router = APIRouter()

# ==================== 用户管理接口 ====================

@router.post("/", response_model=DataResponse[UserRead], status_code=status.HTTP_201_CREATED)
def create_user(
    user_data: UserCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建用户"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    user = user_service.create_user(user_data, created_by=user_context.user.id)
    return success_response(user, "用户创建成功")

@router.get("/", response_model=ListResponse[UserRead])
def get_users(
    skip: int = Query(default=0, ge=0, description="跳过记录数"),
    limit: int = Query(default=100, ge=1, le=1000, description="返回记录数"),
    role: Optional[UserRole] = Query(default=None, description="按角色筛选"),
    status: Optional[UserStatus] = Query(default=None, description="按状态筛选"),
    search: Optional[str] = Query(default=None, description="搜索关键词"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取用户列表"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)

    params = UserSearchParams(
        skip=skip,
        limit=limit,
        role=role,
        status=status,
        search=search
    )

    result = user_service.get_users_with_pagination(params)
    return list_response(result.items, result.total, "获取用户列表成功")

@router.get("/{user_id}", response_model=DataResponse[UserRead])
def get_user(
    user_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取用户详情"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    user = user_service.get_user(user_id)
    user = ensure_found(user, "用户")
    return success_response(user, "获取用户详情成功")

@router.post("/{user_id}", response_model=DataResponse[UserRead])
def update_user(
    user_id: int,
    user_data: UserUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新用户信息"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    updated_user = user_service.update_user(user_id, user_data)
    updated_user = ensure_found(updated_user, "用户")
    return success_response(updated_user, "用户信息更新成功")

# ==================== 密码管理 ====================

@router.post("/{user_id}/change-password", response_model=MessageResponse)
def change_password(
    user_id: int,
    password_data: PasswordChange,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """修改密码"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    success = user_service.change_password(
        user_id,
        password_data.old_password,
        password_data.new_password
    )
    ensure_success(success, "原密码错误或用户不存在")
    return message_response("密码修改成功")

@router.post("/{user_id}/reset-password", response_model=MessageResponse)
def reset_password(
    user_id: int,
    new_password: str,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """重置密码（管理员操作）"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    success = user_service.reset_password(user_id, new_password)
    ensure_success(success, "用户不存在")
    return message_response("密码重置成功")

# ==================== 用户状态管理 ====================

@router.post("/{user_id}/activate", response_model=DataResponse[UserRead])
def activate_user(
    user_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """激活用户"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    user = user_service.activate_user(user_id)
    user = ensure_found(user, "用户")
    return success_response(user, "用户激活成功")

@router.post("/{user_id}/deactivate", response_model=DataResponse[UserRead])
def deactivate_user(
    user_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """停用用户"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    user = user_service.deactivate_user(user_id)
    user = ensure_found(user, "用户")
    return success_response(user, "用户停用成功")

@router.post("/{user_id}/lock", response_model=DataResponse[UserRead])
def lock_user(
    user_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """锁定用户"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    user = user_service.lock_user(user_id)
    user = ensure_found(user, "用户")
    return success_response(user, "用户锁定成功")

@router.post("/{user_id}/delete", response_model=MessageResponse)
def delete_user(
    user_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除用户"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    user_service.delete_user(user_id)
    return message_response("用户删除成功")
