"""
管理端 - 租户管理API
"""
from fastapi import APIRouter, Depends, status, Query
from sqlmodel import Session

from app.db.session import get_global_session
from app.core.dependencies import require_super_admin, UserContext
from app.api.common.responses import DataResponse, ListResponse, MessageResponse, success_response, list_response, message_response
from app.api.common import ensure_found

# 租户相关导入
from app.features.tenants.service import TenantService
from app.features.tenants.schemas import TenantCreate, TenantUpdate, TenantRead
from app.features.tenants.models import TenantPlanTemplate

router = APIRouter()

# ==================== 租户管理接口 ====================

@router.post("/", response_model=DataResponse[TenantRead], status_code=status.HTTP_201_CREATED)
def create_tenant(
    tenant_data: TenantCreate,
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """创建新租户（仅超级管理员）"""
    tenant_service = TenantService(session)
    tenant = tenant_service.create_tenant(tenant_data)
    return success_response(tenant, "租户创建成功")

@router.get("/", response_model=ListResponse[TenantRead])
def get_tenants(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """获取租户列表（仅超级管理员）"""
    tenant_service = TenantService(session)
    tenants = tenant_service.get_tenants(skip=skip, limit=limit)
    return list_response(tenants, len(tenants), "获取租户列表成功")

# ==================== 套餐模板管理 ====================

@router.get("/plans/templates", response_model=ListResponse[TenantPlanTemplate])
def get_plan_templates(
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """获取所有套餐模板（仅超级管理员）"""
    tenant_service = TenantService(session)
    templates = tenant_service.get_plan_templates()
    return list_response(templates, len(templates), "获取套餐模板成功")

@router.get("/code/{code}", response_model=DataResponse[TenantRead])
def get_tenant_by_code(
    code: str,
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """根据代码获取租户详情（仅超级管理员）"""
    tenant_service = TenantService(session)
    tenant = tenant_service.get_tenant_by_code(code)
    tenant = ensure_found(tenant, "租户")
    return success_response(tenant, "获取租户详情成功")

@router.get("/{tenant_id}", response_model=DataResponse[TenantRead])
def get_tenant(
    tenant_id: int,
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """根据ID获取租户详情（仅超级管理员）"""
    tenant_service = TenantService(session)
    tenant = tenant_service.get_tenant(tenant_id)
    tenant = ensure_found(tenant, "租户")
    return success_response(tenant, "获取租户详情成功")

@router.post("/{tenant_id}", response_model=DataResponse[TenantRead])
def update_tenant(
    tenant_id: int,
    tenant_data: TenantUpdate,
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """更新租户信息（仅超级管理员）"""
    tenant_service = TenantService(session)
    tenant = tenant_service.update_tenant(tenant_id, tenant_data)
    return success_response(tenant, "租户信息更新成功")

@router.post("/{tenant_id}/delete", response_model=MessageResponse)
def delete_tenant(
    tenant_id: int,
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """删除租户（软删除）（仅超级管理员）"""
    tenant_service = TenantService(session)
    tenant_service.delete_tenant(tenant_id)
    return message_response("租户删除成功")



@router.post("/{tenant_id}/apply-plan/{plan_code}", response_model=DataResponse[TenantRead])
def apply_plan_template(
    tenant_id: int,
    plan_code: str,
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """为租户应用套餐模板（仅超级管理员）"""
    tenant_service = TenantService(session)
    tenant = tenant_service.apply_plan_template(tenant_id, plan_code)
    return success_response(tenant, "套餐模板应用成功")

# ==================== 租户状态管理 ====================

@router.post("/{tenant_id}/activate", response_model=DataResponse[TenantRead])
def activate_tenant(
    tenant_id: int,
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """激活租户（仅超级管理员）"""
    tenant_service = TenantService(session)
    tenant = tenant_service.activate_tenant(tenant_id)
    return success_response(tenant, "租户激活成功")

@router.post("/{tenant_id}/suspend", response_model=DataResponse[TenantRead])
def suspend_tenant(
    tenant_id: int,
    reason: str = None,
    user_context: UserContext = Depends(require_super_admin),
    session: Session = Depends(get_global_session)
):
    """暂停租户（仅超级管理员）"""
    tenant_service = TenantService(session)
    tenant = tenant_service.suspend_tenant(tenant_id, reason)
    return success_response(tenant, "租户暂停成功")
