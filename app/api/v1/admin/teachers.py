"""
管理端 - 教师管理API
"""
from typing import Optional
from fastapi import APIRouter, Depends, status, Query, UploadFile, File
from sqlmodel import Session

from app.db.session import get_session
from app.core.dependencies import get_user_context, UserContext
from app.api.common.responses import (
    DataResponse, MessageResponse, ListResponse, PageResponse,
    success_response, message_response, list_response, page_response,
    create_error_responses
)
from app.api.common import ensure_found, ensure_success

# 教师相关导入
from app.features.teachers.service import TeacherService
from app.features.teachers.schemas import (
    TeacherCreate, TeacherUpdate, TeacherRead, TeacherList, TeacherDetail,
    TeacherQuery, TeacherTagAssign, TeacherTagBatch, TeacherStatusUpdate
)
from app.features.teachers.models import TeacherCategory, TeacherRegion, TeacherStatus
from app.features.teachers.exceptions import TeacherErrorCode

router = APIRouter()

# 教师模块的错误响应文档
TEACHER_ERROR_RESPONSES = create_error_responses([
    TeacherErrorCode.EMAIL_EXISTS,
    TeacherErrorCode.PHONE_EXISTS,
    TeacherErrorCode.WECHAT_BOUND,
    TeacherErrorCode.STATUS_INVALID,
    TeacherErrorCode.TAG_NOT_FOUND,
    TeacherErrorCode.TAG_ALREADY_ASSIGNED,
    TeacherErrorCode.SLOT_CONFLICT,
    TeacherErrorCode.SLOT_NOT_FOUND,
    TeacherErrorCode.INVALID_TIME_RANGE,
    TeacherErrorCode.BATCH_OPERATION_FAILED,
    TeacherErrorCode.SLOT_NOT_BELONG_TO_TEACHER,
    TeacherErrorCode.INVALID_OPERATION,
    TeacherErrorCode.INVALID_FILE_TYPE,
    TeacherErrorCode.FILE_TOO_LARGE,
    TeacherErrorCode.INVALID_TAG_IDS,
    TeacherErrorCode.INVALID_TIME_FORMAT
])

# ==================== 教师统计接口 ====================

@router.get(
    "/statistics",
    response_model=DataResponse[dict],
    summary="获取教师统计信息",
    description="获取教师数量统计和分布信息"
)
def get_teacher_statistics(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师统计信息"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    stats = teacher_service.get_teachers_statistics()
    return success_response(stats, "获取教师统计信息成功")

# ==================== 教师高级查询接口 ====================

@router.get(
    "/priority",
    response_model=ListResponse[TeacherList],
    summary="按优先级获取教师",
    description="按优先级排序获取激活状态的教师列表"
)
def get_priority_teachers(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """按优先级获取教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    teachers = teacher_service.get_teachers_by_priority()
    teacher_list = []
    for teacher in teachers:
        teacher_dict = teacher.model_dump()
        teacher_dict["tags"] = teacher_service.get_teacher_tags(teacher.id)
        teacher_list.append(TeacherList(**teacher_dict))

    return list_response(teacher_list, len(teacher_list), "获取优先级教师列表成功")

@router.get(
    "/available",
    response_model=ListResponse[TeacherList],
    summary="获取对会员可见的教师",
    description="获取激活且对会员端展示的教师列表"
)
def get_available_teachers(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取对会员可见的教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    teachers = teacher_service.get_available_teachers()
    teacher_list = []
    for teacher in teachers:
        teacher_dict = teacher.model_dump()
        teacher_dict["tags"] = teacher_service.get_teacher_tags(teacher.id)
        teacher_list.append(TeacherList(**teacher_dict))

    return list_response(teacher_list, len(teacher_list), "获取可用教师列表成功")

@router.get(
    "/search",
    response_model=ListResponse[TeacherList],
    summary="搜索教师",
    description="根据关键词搜索教师"
)
def search_teachers(
    keyword: str = Query(..., description="搜索关键词"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """搜索教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    teachers = teacher_service.search_teachers(keyword)
    teacher_list = []
    for teacher in teachers:
        teacher_dict = teacher.model_dump()
        teacher_dict["tags"] = teacher_service.get_teacher_tags(teacher.id)
        teacher_list.append(TeacherList(**teacher_dict))

    return list_response(teacher_list, len(teacher_list), "搜索教师成功")

# ==================== 教师标签批量管理接口 ====================

@router.post(
    "/tags/batch",
    response_model=MessageResponse,
    summary="批量管理教师标签",
    description="批量为多个教师分配或移除标签"
)
def batch_manage_teacher_tags(
    batch_data: TeacherTagBatch,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量管理教师标签"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    if batch_data.action == "assign":
        teacher_service.batch_assign_tags_to_teachers(
            teacher_ids=batch_data.teacher_ids,
            tag_ids=batch_data.tag_ids,
            created_by=user_context.user.id
        )
        return message_response("批量分配标签成功")
    elif batch_data.action == "remove":
        teacher_service.batch_remove_tags_from_teachers(
            teacher_ids=batch_data.teacher_ids,
            tag_ids=batch_data.tag_ids
        )
        return message_response("批量移除标签成功")

# ==================== 教师基础CRUD接口 ====================

@router.post(
    "/",
    response_model=DataResponse[TeacherRead],
    status_code=status.HTTP_201_CREATED,
    responses=TEACHER_ERROR_RESPONSES,
    summary="创建教师",
    description="""
    创建新教师

    **可能的错误码：**
    - `TEACHER_EMAIL_EXISTS`: 邮箱已存在
    - `TEACHER_PHONE_EXISTS`: 手机号已存在
    - `VALIDATION_ERROR`: 数据验证失败
    """
)
def create_teacher(
    teacher_data: TeacherCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    teacher = teacher_service.create_teacher(teacher_data, created_by=user_context.user.id)
    return success_response(teacher, "教师创建成功")

@router.get(
    "/",
    response_model=PageResponse[TeacherList],
    summary="获取教师列表",
    description="获取教师列表，支持分页、筛选和排序"
)
def get_teachers(
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    name: Optional[str] = Query(default=None, description="教师姓名模糊查询"),
    email: Optional[str] = Query(default=None, description="邮箱模糊查询"),
    phone: Optional[str] = Query(default=None, description="手机号模糊查询"),
    status: Optional[TeacherStatus] = Query(default=None, description="教师状态"),
    category: Optional[TeacherCategory] = Query(default=None, description="教师类别"),
    region: Optional[TeacherRegion] = Query(default=None, description="教师地区"),
    tag_ids: Optional[str] = Query(default=None, description="标签ID列表，逗号分隔"),
    sort_by: Optional[str] = Query(default="created_at", description="排序字段"),
    sort_order: Optional[str] = Query(default="desc", description="排序方向"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师列表"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 解析标签ID列表
    tag_id_list = None
    if tag_ids:
        try:
            tag_id_list = [int(tag_id.strip()) for tag_id in tag_ids.split(",") if tag_id.strip()]
        except ValueError:
            tag_id_list = None

    query_params = TeacherQuery(
        page=page,
        size=size,
        name=name,
        email=email,
        phone=phone,
        status=status,
        category=category,
        region=region,
        tag_ids=tag_id_list,
        sort_by=sort_by,
        sort_order=sort_order
    )

    teachers, total = teacher_service.get_teachers(query_params)

    # 为每个教师添加标签信息
    teacher_list = []
    for teacher in teachers:
        teacher_dict = teacher.model_dump()
        teacher_dict["tags"] = teacher_service.get_teacher_tags(teacher.id)
        teacher_list.append(TeacherList(**teacher_dict))

    return page_response(teacher_list, total, page, size, "获取教师列表成功")

@router.get(
    "/{teacher_id}",
    response_model=DataResponse[TeacherDetail],
    summary="获取教师详情",
    description="根据ID获取教师详情，包含关联的标签信息"
)
def get_teacher(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师详情"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    teacher = teacher_service.get_teacher(teacher_id)
    teacher = ensure_found(teacher, "教师")

    # 获取教师标签
    teacher_tags = teacher_service.get_teacher_tags(teacher_id)
    teacher_detail = TeacherDetail(**teacher.model_dump(), tags=teacher_tags)

    return success_response(teacher_detail, "获取教师详情成功")

@router.post(
    "/{teacher_id}/update",
    response_model=DataResponse[TeacherRead],
    summary="更新教师信息",
    description="更新教师基本信息"
)
def update_teacher(
    teacher_id: int,
    teacher_data: TeacherUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新教师信息"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    updated_teacher = teacher_service.update_teacher(teacher_id, teacher_data)
    return success_response(updated_teacher, "教师信息更新成功")

@router.post(
    "/{teacher_id}/delete",
    response_model=MessageResponse,
    summary="删除教师",
    description="删除指定教师"
)
def delete_teacher(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    teacher_service.delete_teacher(teacher_id)
    return message_response("教师删除成功")

# ==================== 教师状态管理接口 ====================

@router.post(
    "/{teacher_id}/status",
    response_model=DataResponse[TeacherRead],
    summary="更新教师状态",
    description="更新教师状态（激活/停用/暂停）"
)
def update_teacher_status(
    teacher_id: int,
    status_data: TeacherStatusUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新教师状态"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    updated_teacher = teacher_service.update_teacher_status(teacher_id, status_data)
    return success_response(updated_teacher, "教师状态更新成功")

@router.post(
    "/{teacher_id}/activate",
    response_model=DataResponse[TeacherRead],
    summary="激活教师",
    description="激活指定教师"
)
def activate_teacher(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """激活教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    activated_teacher = teacher_service.activate_teacher(teacher_id)
    return success_response(activated_teacher, "教师激活成功")

@router.post(
    "/{teacher_id}/deactivate",
    response_model=DataResponse[TeacherRead],
    summary="停用教师",
    description="停用指定教师"
)
def deactivate_teacher(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """停用教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    deactivated_teacher = teacher_service.deactivate_teacher(teacher_id)
    return success_response(deactivated_teacher, "教师停用成功")

# ==================== 教师标签管理接口 ====================

@router.post(
    "/{teacher_id}/tags",
    response_model=MessageResponse,
    summary="为教师分配标签",
    description="为指定教师分配标签"
)
def assign_tags_to_teacher(
    teacher_id: int,
    tag_data: TeacherTagAssign,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """为教师分配标签"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    teacher_service.assign_tags_to_teacher(
        teacher_id=teacher_id,
        tag_ids=tag_data.tag_ids,
        created_by=user_context.user.id
    )
    return message_response("教师标签分配成功")

@router.get(
    "/{teacher_id}/tags",
    response_model=ListResponse[dict],
    summary="获取教师标签",
    description="获取指定教师的所有标签"
)
def get_teacher_tags(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师标签"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 确保教师存在
    teacher = teacher_service.get_teacher(teacher_id)
    ensure_found(teacher, "教师")

    tags = teacher_service.get_teacher_tags(teacher_id)
    return list_response(tags, len(tags), "获取教师标签成功")

@router.delete(
    "/{teacher_id}/tags/{tag_id}",
    response_model=MessageResponse,
    summary="移除教师标签",
    description="移除教师的指定标签"
)
def remove_tag_from_teacher(
    teacher_id: int,
    tag_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """移除教师标签"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    teacher_service.remove_tag_from_teacher(teacher_id, tag_id)
    return message_response("教师标签移除成功")

# ==================== 教师头像上传接口 ====================

@router.post(
    "/{teacher_id}/avatar",
    response_model=DataResponse[dict],
    summary="上传教师头像",
    description="上传教师头像图片"
)
def upload_teacher_avatar(
    teacher_id: int,
    file: UploadFile = File(...),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """上传教师头像"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 确保教师存在
    teacher = teacher_service.get_teacher(teacher_id)
    ensure_found(teacher, "教师")

    avatar_url = teacher_service.upload_teacher_avatar(teacher_id, file)
    return success_response({"avatar_url": avatar_url}, "教师头像上传成功")

@router.delete(
    "/{teacher_id}/avatar",
    response_model=MessageResponse,
    summary="删除教师头像",
    description="删除教师头像"
)
def delete_teacher_avatar(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除教师头像"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 确保教师存在
    teacher = teacher_service.get_teacher(teacher_id)
    ensure_found(teacher, "教师")

    teacher_service.delete_teacher_avatar(teacher_id)
    return message_response("教师头像删除成功")
