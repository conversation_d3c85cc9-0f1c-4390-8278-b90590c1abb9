"""
管理端 - 会员固定课位管理API
"""
from typing import Optional
from fastapi import APIRouter, Depends, status, Query
from sqlmodel import Session

from app.db.session import get_session
from app.core.dependencies import get_user_context, UserContext
from app.api.common.responses import (
    DataResponse, MessageResponse, ListResponse, PageResponse,
    success_response, message_response, list_response, page_response,
    create_error_responses
)
from app.api.common import ensure_found

# 固定课位相关导入
from app.features.members.fixed_lock_service import MemberFixedSlotLockService
from app.features.members.fixed_lock_schemas import (
    MemberFixedSlotLockCreate, MemberFixedSlotLockUpdate, MemberFixedSlotLockResponse,
    MemberFixedSlotLockList, MemberFixedSlotLockQuery, MemberFixedSlotLockStatusChange,
    MemberFixedSlotLockBatchCreate, MemberFixedSlotLockBatchUpdate, MemberFixedSlotLockBatchDelete,
    MemberFixedSlotLockBatchStatusChange, AvailableSlotQuery, AvailableSlotResponse,
    LockConflictCheck, LockConflictResult, LockOperationResult, BatchLockOperationResult
)
from app.features.members.fixed_lock_models import MemberFixedSlotLock, MemberFixedSlotLockStatus
from app.features.members.fixed_lock_exceptions import MemberFixedSlotLockErrorCode

router = APIRouter()

# 固定课位锁定模块的错误响应文档
LOCK_ERROR_RESPONSES = create_error_responses([
    MemberFixedSlotLockErrorCode.MEMBER_NOT_FOUND,
    MemberFixedSlotLockErrorCode.TEACHER_SLOT_NOT_FOUND,
    MemberFixedSlotLockErrorCode.SLOT_NOT_AVAILABLE,
    MemberFixedSlotLockErrorCode.SLOT_NOT_VISIBLE,
    MemberFixedSlotLockErrorCode.SLOT_ALREADY_LOCKED,
    MemberFixedSlotLockErrorCode.LOCK_CONFLICT
])

# ==================== 查询和检查接口 ====================

@router.get(
    "/available-slots",
    response_model=ListResponse[AvailableSlotResponse],
    summary="获取可用的固定课位",
    description="获取指定条件下可用的固定课位列表"
)
def get_available_slots(
    query: AvailableSlotQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取可用的固定课位"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)
    
    available_slots = lock_service.get_available_slots(query)
    return list_response(available_slots, len(available_slots), "获取可用固定课位成功")

@router.post(
    "/check-conflict",
    response_model=DataResponse[LockConflictResult],
    summary="检查课位锁定冲突",
    description="检查指定课位是否存在锁定冲突"
)
def check_lock_conflict(
    conflict_check: LockConflictCheck,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """检查课位锁定冲突"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)
    
    conflict_result = lock_service.check_lock_conflict(conflict_check)
    return success_response(conflict_result, "冲突检查完成")

# ==================== 批量操作接口 ====================

@router.post(
    "/batch/create",
    response_model=DataResponse[BatchLockOperationResult],
    summary="批量创建固定课位锁定",
    description="批量为会员创建固定课位锁定"
)
def batch_create_fixed_slot_locks(
    batch_data: MemberFixedSlotLockBatchCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量创建固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)

    result = lock_service.batch_create_locks(batch_data, created_by=user_context.user.id)
    return success_response(result, f"批量创建固定课位锁定完成，成功 {result.success_count} 个，失败 {result.failure_count} 个")

@router.post(
    "/batch/update",
    response_model=DataResponse[BatchLockOperationResult],
    summary="批量更新固定课位锁定",
    description="批量更新固定课位锁定信息"
)
def batch_update_fixed_slot_locks(
    batch_data: MemberFixedSlotLockBatchUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量更新固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)

    result = lock_service.batch_update_locks(batch_data)
    return success_response(result, f"批量更新固定课位锁定完成，成功 {result.success_count} 个，失败 {result.failure_count} 个")

@router.post(
    "/batch/delete",
    response_model=DataResponse[BatchLockOperationResult],
    summary="批量删除固定课位锁定",
    description="批量删除固定课位锁定"
)
def batch_delete_fixed_slot_locks(
    batch_data: MemberFixedSlotLockBatchDelete,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量删除固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)

    result = lock_service.batch_delete_locks(batch_data)
    return success_response(result, f"批量删除固定课位锁定完成，成功 {result.success_count} 个，失败 {result.failure_count} 个")

@router.post(
    "/batch/status",
    response_model=DataResponse[BatchLockOperationResult],
    summary="批量更新固定课位锁定状态",
    description="批量更新固定课位锁定状态"
)
def batch_update_fixed_slot_lock_status(
    batch_data: MemberFixedSlotLockBatchStatusChange,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量更新固定课位锁定状态"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)

    result = lock_service.batch_update_lock_status(batch_data)
    return success_response(result, f"批量更新固定课位锁定状态完成，成功 {result.success_count} 个，失败 {result.failure_count} 个")

# ==================== 基础CRUD接口 ====================

@router.post(
    "/",
    response_model=DataResponse[LockOperationResult],
    status_code=status.HTTP_201_CREATED,
    responses=LOCK_ERROR_RESPONSES,
    summary="创建固定课位锁定",
    description="""
    为会员创建固定课位锁定

    **可能的错误码：**
    - `MEMBER_NOT_FOUND`: 会员不存在
    - `TEACHER_SLOT_NOT_FOUND`: 教师课位不存在
    - `SLOT_ALREADY_LOCKED`: 课位已被锁定
    - `LOCK_CONFLICT`: 存在时间冲突
    """
)
def create_fixed_slot_lock(
    lock_data: MemberFixedSlotLockCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)

    result = lock_service.create_lock(lock_data, created_by=user_context.user.id)
    return success_response(result, "固定课位锁定创建成功")

@router.get(
    "/",
    response_model=PageResponse[MemberFixedSlotLockList],
    summary="获取固定课位锁定列表",
    description="获取固定课位锁定列表，支持分页和筛选"
)
def get_fixed_slot_locks(
    query: MemberFixedSlotLockQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取固定课位锁定列表"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)

    locks, total = lock_service.get_locks(query)
    lock_list = [MemberFixedSlotLockList(**lock.model_dump()) for lock in locks]

    return page_response(lock_list, total, query.page, query.size, "获取固定课位锁定列表成功")

@router.get(
    "/{lock_id}",
    response_model=DataResponse[MemberFixedSlotLockResponse],
    summary="获取固定课位锁定详情",
    description="根据ID获取固定课位锁定详情"
)
def get_fixed_slot_lock(
    lock_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取固定课位锁定详情"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)
    
    lock = lock_service.get_lock(lock_id)
    lock = ensure_found(lock, "固定课位锁定")
    
    return success_response(lock, "获取固定课位锁定详情成功")

@router.post(
    "/{lock_id}/update",
    response_model=DataResponse[LockOperationResult],
    summary="更新固定课位锁定",
    description="更新固定课位锁定信息"
)
def update_fixed_slot_lock(
    lock_id: int,
    lock_data: MemberFixedSlotLockUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)
    
    result = lock_service.update_lock(lock_id, lock_data)
    return success_response(result, "固定课位锁定更新成功")

@router.post(
    "/{lock_id}/status",
    response_model=DataResponse[LockOperationResult],
    summary="更新固定课位锁定状态",
    description="更新固定课位锁定状态"
)
def update_fixed_slot_lock_status(
    lock_id: int,
    status_data: MemberFixedSlotLockStatusChange,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新固定课位锁定状态"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)
    
    result = lock_service.update_lock_status(lock_id, status_data)
    return success_response(result, "固定课位锁定状态更新成功")

@router.post(
    "/{lock_id}/delete",
    response_model=MessageResponse,
    summary="删除固定课位锁定",
    description="删除指定的固定课位锁定"
)
def delete_fixed_slot_lock(
    lock_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id
    lock_service = MemberFixedSlotLockService(session, tenant_id)
    
    lock_service.delete_lock(lock_id)
    return message_response("固定课位锁定删除成功")
