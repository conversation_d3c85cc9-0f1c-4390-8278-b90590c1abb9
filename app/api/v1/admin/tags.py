"""
管理端 - 标签管理API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, status, Query
from sqlmodel import Session

from app.db.session import get_session
from app.core.dependencies import get_user_context, UserContext
from app.api.common.responses import (
    DataResponse, MessageResponse, ListResponse, PageResponse,
    success_response, message_response, list_response, page_response,
    create_error_responses
)
from app.api.common import ensure_found

# 标签相关导入
from app.features.tags.service import get_tag_category_service, get_tag_service
from app.features.tags.schemas import (
    TagCategoryCreate, TagCategoryUpdate, TagCategoryRead, TagCategoryList, TagCategoryQuery,
    TagCreate, TagUpdate, TagRead, TagList, TagWithCategory, TagQuery,
    TagBatchCreate, TagBatchUpdate
)
from app.features.tags.models import TagStatus
from app.features.tags.exceptions import TagErrorCode

router = APIRouter()

# 标签模块的错误响应文档
TAG_ERROR_RESPONSES = create_error_responses([
    TagErrorCode.CATEGORY_NAME_EXISTS,
    TagErrorCode.TAG_NAME_EXISTS,
    TagErrorCode.CATEGORY_HAS_TAGS,
    TagErrorCode.TAG_IN_USE
])

# ==================== 标签分类管理接口 ====================

@router.post(
    "/categories",
    response_model=DataResponse[TagCategoryRead],
    status_code=status.HTTP_201_CREATED,
    responses=TAG_ERROR_RESPONSES,
    summary="创建标签分类",
    description="""
    创建新的标签分类

    **可能的错误码：**
    - `TAG_CATEGORY_NAME_EXISTS`: 分类名称已存在
    - `VALIDATION_ERROR`: 数据验证失败
    """
)
def create_tag_category(
    category_data: TagCategoryCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建标签分类"""
    tenant_id = user_context.tenant_context.tenant_id
    category_service = get_tag_category_service(session, tenant_id)
    category = category_service.create_category(category_data, created_by=user_context.user.id)
    return success_response(category, "标签分类创建成功")

@router.get(
    "/categories",
    response_model=PageResponse[TagCategoryList],
    summary="获取标签分类列表",
    description="获取标签分类列表，支持分页和搜索"
)
def get_tag_categories(
    name: Optional[str] = Query(default=None, description="分类名称模糊查询"),
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取标签分类列表"""
    tenant_id = user_context.tenant_context.tenant_id
    category_service = get_tag_category_service(session, tenant_id)

    query_params = TagCategoryQuery(name=name, page=page, size=size)
    categories, total = category_service.get_categories(query_params)

    # 转换为列表响应模型，包含标签数量
    category_list = []
    for category in categories:
        category_dict = category.model_dump()
        # 获取该分类下的标签数量
        tag_count = category_service.get_category_tag_count(category.id)
        category_with_count = {**category_dict, "tag_count": tag_count}
        category_list.append(TagCategoryList(**category_with_count))

    return page_response(category_list, total, page, size, "获取标签分类列表成功")

@router.get(
    "/categories/{category_id}",
    response_model=DataResponse[TagCategoryRead],
    summary="获取标签分类详情",
    description="根据ID获取标签分类详情"
)
def get_tag_category(
    category_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取标签分类详情"""
    tenant_id = user_context.tenant_context.tenant_id
    category_service = get_tag_category_service(session, tenant_id)
    category = category_service.get_category(category_id)
    category = ensure_found(category, "标签分类")
    return success_response(category, "获取标签分类详情成功")

@router.post(
    "/categories/{category_id}",
    response_model=DataResponse[TagCategoryRead],
    responses=TAG_ERROR_RESPONSES,
    summary="更新标签分类",
    description="""
    更新标签分类信息

    **可能的错误码：**
    - `TAG_CATEGORY_NAME_EXISTS`: 分类名称已存在
    - `RESOURCE_NOT_FOUND`: 分类不存在
    """
)
def update_tag_category(
    category_id: int,
    category_data: TagCategoryUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新标签分类"""
    tenant_id = user_context.tenant_context.tenant_id
    category_service = get_tag_category_service(session, tenant_id)
    category = category_service.update_category(category_id, category_data)
    return success_response(category, "标签分类更新成功")

@router.delete(
    "/categories/{category_id}",
    response_model=MessageResponse,
    responses=TAG_ERROR_RESPONSES,
    summary="删除标签分类",
    description="""
    删除标签分类

    **可能的错误码：**
    - `TAG_CATEGORY_HAS_TAGS`: 分类下还有标签，无法删除
    - `RESOURCE_NOT_FOUND`: 分类不存在
    """
)
def delete_tag_category(
    category_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除标签分类"""
    tenant_id = user_context.tenant_context.tenant_id
    category_service = get_tag_category_service(session, tenant_id)
    category_service.delete_category(category_id)
    return message_response("标签分类删除成功")

# ==================== 标签管理接口 ====================

# 便捷查询接口（需要在参数路径之前定义）
@router.get(
    "/active",
    response_model=ListResponse[TagList],
    summary="获取所有激活状态的标签",
    description="获取所有激活状态的标签，按分类排序"
)
def get_active_tags(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取所有激活状态的标签"""
    tenant_id = user_context.tenant_context.tenant_id
    tag_service = get_tag_service(session, tenant_id)

    query_params = TagQuery(
        status=TagStatus.ACTIVE,
        page=1,
        size=100  # 使用最大允许的页面大小
    )

    tags_with_category, total = tag_service.get_tags_with_category(query_params)
    tag_list = [TagList(**tag_dict) for tag_dict in tags_with_category]

    return list_response(tag_list, total, "获取激活标签列表成功")

# 批量操作接口（需要在参数路径之前定义）
@router.post(
    "/batch",
    response_model=DataResponse[List[TagRead]],
    status_code=status.HTTP_201_CREATED,
    responses=TAG_ERROR_RESPONSES,
    summary="批量创建标签",
    description="""
    批量创建标签

    **可能的错误码：**
    - `TAG_NAME_EXISTS`: 某个标签名称已存在
    - `VALIDATION_ERROR`: 数据验证失败
    """
)
def batch_create_tags(
    batch_data: TagBatchCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量创建标签"""
    tenant_id = user_context.tenant_context.tenant_id
    tag_service = get_tag_service(session, tenant_id)
    created_tags = tag_service.batch_create_tags(batch_data, created_by=user_context.user.id)
    return success_response(created_tags, f"批量创建标签成功，共创建 {len(created_tags)} 个标签")

@router.post(
    "/batch/update",
    response_model=DataResponse[List[TagRead]],
    responses=TAG_ERROR_RESPONSES,
    summary="批量更新标签",
    description="""
    批量更新标签

    **可能的错误码：**
    - `TAG_NAME_EXISTS`: 某个标签名称已存在
    - `RESOURCE_NOT_FOUND`: 某个标签不存在
    """
)
def batch_update_tags(
    batch_data: TagBatchUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量更新标签"""
    tenant_id = user_context.tenant_context.tenant_id
    tag_service = get_tag_service(session, tenant_id)
    updated_tags = tag_service.batch_update_tags(batch_data)
    return success_response(updated_tags, f"批量更新标签成功，共更新 {len(updated_tags)} 个标签")

@router.post(
    "/",
    response_model=DataResponse[TagRead],
    status_code=status.HTTP_201_CREATED,
    responses=TAG_ERROR_RESPONSES,
    summary="创建标签",
    description="""
    创建新标签

    **可能的错误码：**
    - `TAG_NAME_EXISTS`: 标签名称已存在
    - `VALIDATION_ERROR`: 数据验证失败
    """
)
def create_tag(
    tag_data: TagCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建标签"""
    tenant_id = user_context.tenant_context.tenant_id
    tag_service = get_tag_service(session, tenant_id)
    tag = tag_service.create_tag(tag_data, created_by=user_context.user.id)
    return success_response(tag, "标签创建成功")

@router.get(
    "/",
    response_model=PageResponse[TagWithCategory],
    summary="获取标签列表",
    description="获取标签列表，支持分页、搜索和筛选"
)
def get_tags(
    name: Optional[str] = Query(default=None, description="标签名称模糊查询"),
    category_id: Optional[int] = Query(default=None, description="标签分类ID"),
    status: Optional[TagStatus] = Query(default=None, description="标签状态"),
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取标签列表"""
    tenant_id = user_context.tenant_context.tenant_id
    tag_service = get_tag_service(session, tenant_id)

    query_params = TagQuery(
        name=name,
        category_id=category_id,
        status=status,
        page=page,
        size=size
    )

    tags_with_category, total = tag_service.get_tags_with_category(query_params)
    tag_list = [TagWithCategory(**tag_dict) for tag_dict in tags_with_category]

    return page_response(tag_list, total, page, size, "获取标签列表成功")

@router.get(
    "/{tag_id}",
    response_model=DataResponse[TagRead],
    summary="获取标签详情",
    description="根据ID获取标签详情"
)
def get_tag(
    tag_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取标签详情"""
    tenant_id = user_context.tenant_context.tenant_id
    tag_service = get_tag_service(session, tenant_id)
    tag = tag_service.get_tag(tag_id)
    tag = ensure_found(tag, "标签")
    return success_response(tag, "获取标签详情成功")

@router.post(
    "/{tag_id}",
    response_model=DataResponse[TagRead],
    responses=TAG_ERROR_RESPONSES,
    summary="更新标签",
    description="""
    更新标签信息

    **可能的错误码：**
    - `TAG_NAME_EXISTS`: 标签名称已存在
    - `RESOURCE_NOT_FOUND`: 标签不存在
    """
)
def update_tag(
    tag_id: int,
    tag_data: TagUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新标签"""
    tenant_id = user_context.tenant_context.tenant_id
    tag_service = get_tag_service(session, tenant_id)
    tag = tag_service.update_tag(tag_id, tag_data)
    return success_response(tag, "标签更新成功")

@router.delete(
    "/{tag_id}",
    response_model=MessageResponse,
    responses=TAG_ERROR_RESPONSES,
    summary="删除标签",
    description="""
    删除标签

    **可能的错误码：**
    - `TAG_IN_USE`: 标签正在使用中，无法删除
    - `RESOURCE_NOT_FOUND`: 标签不存在
    """
)
def delete_tag(
    tag_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除标签"""
    tenant_id = user_context.tenant_context.tenant_id
    tag_service = get_tag_service(session, tenant_id)
    tag_service.delete_tag(tag_id)
    return message_response("标签删除成功")



@router.get(
    "/categories/{category_id}/tags",
    response_model=ListResponse[TagList],
    summary="获取指定分类下的所有标签",
    description="获取指定分类下的所有标签，不分页"
)
def get_tags_by_category(
    category_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取指定分类下的所有标签"""
    tenant_id = user_context.tenant_context.tenant_id
    tag_service = get_tag_service(session, tenant_id)

    # 首先验证分类是否存在
    category_service = get_tag_category_service(session, tenant_id)
    category = category_service.get_category(category_id)
    ensure_found(category, "标签分类")

    # 获取该分类下的所有标签
    tags = tag_service.get_tags_by_category(category_id)
    tag_list = [TagList(**tag.model_dump()) for tag in tags]

    return list_response(tag_list, len(tag_list), f"获取分类 '{category.name}' 下的标签列表成功")
